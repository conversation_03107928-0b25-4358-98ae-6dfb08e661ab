# SilverStripe CMS with MSSQL Docker Setup

This is a development Docker Compose setup for SilverStripe CMS with Microsoft SQL Server.

## Prerequisites

- Docker
- Docker Compose

## Quick Start

1. Copy the environment file:
   ```bash
   cp .env.example .env
   ```

2. Build and start the containers:
   ```bash
   docker-compose up --build
   ```

3. Wait for the containers to start (MSSQL takes a moment to initialize)

4. Access the application:
   - SilverStripe CMS: http://localhost:8080
   - Database Admin (Adminer): http://localhost:8081

## Services

- **web**: SilverStripe CMS running on Apache with PHP 8.1
- **mssql**: Microsoft SQL Server 2019 Express
- **phpmyadmin**: Adminer for database management

## Default Credentials

### SilverStripe Admin
- Username: admin
- Password: admin123

### MSSQL Database
- Server: localhost:1433
- Username: sa
- Password: YourStrong@Passw0rd
- Database: silverstripe

## Configuration

### Environment Variables

Edit the `.env` file to customize:
- Database credentials
- Admin user credentials
- Environment type

### Database Configuration

The database configuration is in `app/_config/database.yml`. The MSSQL connection is configured to use the `silverstripe/mssql` module.

## Development

### Installing Dependencies

Dependencies are installed automatically during the Docker build. To install additional packages:

```bash
docker-compose exec web composer require package/name
```

### Running Commands

Execute SilverStripe commands:

```bash
docker-compose exec web vendor/bin/sake dev/build
```

### Logs

View logs:

```bash
docker-compose logs web
docker-compose logs mssql
```

## Troubleshooting

1. **MSSQL Connection Issues**: Ensure the MSSQL container is fully started before the web container tries to connect.

2. **Permission Issues**: If you encounter permission issues, run:
   ```bash
   docker-compose exec web chown -R www-data:www-data /var/www/html
   ```

3. **Database Not Created**: The init script should create the database automatically. If not, connect to MSSQL and run the script manually.

## Production Notes

This setup is for development only. For production:
- Use proper secrets management
- Configure SSL/TLS
- Use production-grade database settings
- Implement proper backup strategies
