version: '3.8'

services:
  web:
    build: .
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
      - ./docker/apache/sites-available:/etc/apache2/sites-available
    environment:
      - SS_ENVIRONMENT_TYPE=dev
      - SS_DATABASE_SERVER=mssql
      - SS_DATABASE_NAME=${DB_NAME:-silverstripe}
      - SS_DATABASE_USERNAME=${DB_USER:-sa}
      - SS_DATABASE_PASSWORD=${DB_PASSWORD:-YourStrong@Passw0rd}
      - SS_DEFAULT_ADMIN_USERNAME=${ADMIN_USER:-admin}
      - SS_DEFAULT_ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin123}
    depends_on:
      - mssql
    networks:
      - silverstripe-network

  mssql:
    image: mcr.microsoft.com/mssql/server:2019-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=${DB_PASSWORD:-YourStrong@Passw0rd}
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - mssql_data:/var/opt/mssql
      - ./docker/mssql/init:/docker-entrypoint-initdb.d
    networks:
      - silverstripe-network

  adminer:
    image: adminer
    ports:
      - "8081:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=mssql
    depends_on:
      - mssql
    networks:
      - silverstripe-network

volumes:
  mssql_data:

networks:
  silverstripe-network:
    driver: bridge
